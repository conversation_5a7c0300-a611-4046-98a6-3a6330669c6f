import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';
import 'dart:async';

import '../../../data/models/simple_resume_model.dart';
import '../../../data/models/resume_template_model.dart';
import '../../../domain/usecases/resume_usecases.dart';
import '../../../domain/usecases/activity_usecases.dart';
import '../../../data/models/activity_model.dart';
import '../../../core/services/purchase_manager_service.dart';
import '../../../data/models/user_export_count_model.dart';

part 'resume_state.dart';

class ResumeCubit extends Cubit<ResumeState> {
  final ResumeUseCases _resumeUseCases;
  final ActivityUseCases _activityUseCases;
  final Uuid _uuid = const Uuid();
  Timer? _autoSaveTimer;

  ResumeCubit(this._resumeUseCases, this._activityUseCases) : super(const ResumeState());

  // Getter to access resume use cases for other cubits
  ResumeUseCases get resumeUseCases => _resumeUseCases;

  @override
  Future<void> close() {
    _autoSaveTimer?.cancel();
    return super.close();
  }

  void loadResume(String resumeId) async {
    print('ResumeCubit: Loading resume with ID: $resumeId');
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      final resume = await _resumeUseCases.getResume(resumeId);
      print('ResumeCubit: Resume loaded successfully');
      print('ResumeCubit: Work experience count: ${resume.workExperience.length}');
      print('ResumeCubit: Education count: ${resume.education.length}');
      print('ResumeCubit: Projects count: ${resume.projects.length}');
      print('ResumeCubit: Skills count: ${resume.skills.length}');

      emit(state.copyWith(
        currentResume: resume,
        isLoading: false,
        errorMessage: null,
      ));
    } catch (e) {
      print('ResumeCubit: Error loading resume: $e');
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      ));
    }
  }

  void createNewResume() {
    final newResume = ResumeModel(
      id: _uuid.v4(),
      personalInfo: const PersonalInfoModel(
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        address: '',
        city: '',
        state: '',
        zipCode: '',
        country: '',
      ),
      summary: '',
      workExperience: [],
      education: [],
      projects: [],
      skills: [],
      languages: [],
      certifications: [],
      socialMedia: [],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    emit(state.copyWith(
      currentResume: newResume,
      hasUnsavedChanges: true,
    ));

    // Track activity
    _trackActivity(
      type: ActivityType.resumeCreated,
      resumeId: newResume.id,
      resumeName: _getResumeName(newResume),
    );
  }

  void loadResumeFromModel(ResumeModel resume) {
    emit(state.copyWith(
      currentResume: resume,
      hasUnsavedChanges: false,
      isLoading: false,
      errorMessage: null,
    ));

    // Track resume viewed activity
    _trackActivity(
      type: ActivityType.resumeViewed,
      resumeId: resume.id,
      resumeName: _getResumeName(resume),
    );
  }

  void loadLastResume() async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      // Try to load the last resume from local storage first
      final localResume = await _resumeUseCases.getLocalResume();
      if (localResume != null) {
        emit(state.copyWith(
          currentResume: localResume,
          isLoading: false,
          hasUnsavedChanges: false,
          errorMessage: null,
        ));
        return;
      }

      // If no local resume, just clear the loading state
      emit(state.copyWith(
        isLoading: false,
        errorMessage: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      ));
    }
  }

  void updatePersonalInfo(PersonalInfoModel personalInfo) {
    if (state.currentResume != null) {
      final updatedResume = state.currentResume!.copyWith(
        personalInfo: personalInfo,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
      _scheduleAutoSave();

      // Track section edit activity
      _trackActivity(
        type: ActivityType.sectionEdited,
        resumeId: updatedResume.id,
        resumeName: _getResumeName(updatedResume),
        sectionName: 'Personal Information',
      );
    }
  }

  void updateSummary(String summary) {
    if (state.currentResume != null) {
      final updatedResume = state.currentResume!.copyWith(
        summary: summary,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
      _scheduleAutoSave();
    }
  }

  void _scheduleAutoSave() {
    // Cancel any existing timer
    _autoSaveTimer?.cancel();

    // Schedule auto-save after 3 seconds of inactivity
    _autoSaveTimer = Timer(const Duration(seconds: 3), () {
      if (state.hasUnsavedChanges && !state.isSaving) {
        saveResume();
      }
    });
  }

  void addWorkExperience(WorkExperienceModel workExperience) {
    if (state.currentResume != null) {
      final updatedExperience = List<WorkExperienceModel>.from(
        state.currentResume!.workExperience,
      )..add(workExperience);

      final updatedResume = state.currentResume!.copyWith(
        workExperience: updatedExperience,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
      _scheduleAutoSave();
    }
  }

  void updateWorkExperience(int index, WorkExperienceModel workExperience) {
    if (state.currentResume != null && index < state.currentResume!.workExperience.length) {
      final updatedExperience = List<WorkExperienceModel>.from(
        state.currentResume!.workExperience,
      );
      updatedExperience[index] = workExperience;

      final updatedResume = state.currentResume!.copyWith(
        workExperience: updatedExperience,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
    }
  }

  void removeWorkExperience(int index) {
    if (state.currentResume != null && index < state.currentResume!.workExperience.length) {
      final updatedExperience = List<WorkExperienceModel>.from(
        state.currentResume!.workExperience,
      )..removeAt(index);

      final updatedResume = state.currentResume!.copyWith(
        workExperience: updatedExperience,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
    }
  }

  void addEducation(EducationModel education) {
    if (state.currentResume != null) {
      final updatedEducation = List<EducationModel>.from(
        state.currentResume!.education,
      )..add(education);

      final updatedResume = state.currentResume!.copyWith(
        education: updatedEducation,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
      _scheduleAutoSave();
    }
  }

  void updateEducation(int index, EducationModel education) {
    if (state.currentResume != null && index < state.currentResume!.education.length) {
      final updatedEducation = List<EducationModel>.from(
        state.currentResume!.education,
      );
      updatedEducation[index] = education;

      final updatedResume = state.currentResume!.copyWith(
        education: updatedEducation,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
    }
  }

  void removeEducation(int index) {
    if (state.currentResume != null && index < state.currentResume!.education.length) {
      final updatedEducation = List<EducationModel>.from(
        state.currentResume!.education,
      )..removeAt(index);

      final updatedResume = state.currentResume!.copyWith(
        education: updatedEducation,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
    }
  }

  void addProject(ProjectModel project) {
    if (state.currentResume != null) {
      final updatedProjects = List<ProjectModel>.from(
        state.currentResume!.projects,
      )..add(project);

      final updatedResume = state.currentResume!.copyWith(
        projects: updatedProjects,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
      _scheduleAutoSave();
    }
  }

  void updateProject(int index, ProjectModel project) {
    if (state.currentResume != null && index < state.currentResume!.projects.length) {
      final updatedProjects = List<ProjectModel>.from(
        state.currentResume!.projects,
      );
      updatedProjects[index] = project;

      final updatedResume = state.currentResume!.copyWith(
        projects: updatedProjects,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
    }
  }

  void removeProject(int index) {
    if (state.currentResume != null && index < state.currentResume!.projects.length) {
      final updatedProjects = List<ProjectModel>.from(
        state.currentResume!.projects,
      )..removeAt(index);

      final updatedResume = state.currentResume!.copyWith(
        projects: updatedProjects,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
    }
  }

  void addSkillCategory(SkillCategoryModel skillCategory) {
    if (state.currentResume != null) {
      final updatedSkills = List<SkillCategoryModel>.from(
        state.currentResume!.skills,
      )..add(skillCategory);

      final updatedResume = state.currentResume!.copyWith(
        skills: updatedSkills,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
      _scheduleAutoSave();
    }
  }

  void updateSkillCategory(int index, SkillCategoryModel skillCategory) {
    if (state.currentResume != null && index < state.currentResume!.skills.length) {
      final updatedSkills = List<SkillCategoryModel>.from(
        state.currentResume!.skills,
      );
      updatedSkills[index] = skillCategory;

      final updatedResume = state.currentResume!.copyWith(
        skills: updatedSkills,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
    }
  }

  void removeSkillCategory(int index) {
    if (state.currentResume != null && index < state.currentResume!.skills.length) {
      final updatedSkills = List<SkillCategoryModel>.from(
        state.currentResume!.skills,
      )..removeAt(index);

      final updatedResume = state.currentResume!.copyWith(
        skills: updatedSkills,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
    }
  }

  void saveResume() async {
    if (state.currentResume == null) return;

    emit(state.copyWith(isSaving: true, errorMessage: null));

    try {
      await _resumeUseCases.saveResume(state.currentResume!);
      emit(state.copyWith(
        isSaving: false,
        hasUnsavedChanges: false,
        errorMessage: null,
        lastSavedAt: DateTime.now(),
      ));

      // Track save activity
      _trackActivity(
        type: ActivityType.resumeSaved,
        resumeId: state.currentResume!.id,
        resumeName: _getResumeName(state.currentResume!),
      );
    } catch (e) {
      // Check if it's a cloud sync error but local save succeeded
      final isCloudSyncError = e.toString().contains('Saved locally, but failed to sync with cloud');

      emit(state.copyWith(
        isSaving: false,
        hasUnsavedChanges: isCloudSyncError ? false : true, // If saved locally, mark as saved
        errorMessage: e.toString(),
        lastSavedAt: isCloudSyncError ? DateTime.now() : null,
      ));
    }
  }

  void retrySync() async {
    if (state.currentResume == null) return;

    emit(state.copyWith(isSaving: true, errorMessage: null));

    try {
      await _resumeUseCases.saveResume(state.currentResume!);
      emit(state.copyWith(
        isSaving: false,
        hasUnsavedChanges: false,
        errorMessage: null,
        lastSavedAt: DateTime.now(),
      ));
    } catch (e) {
      emit(state.copyWith(
        isSaving: false,
        errorMessage: e.toString(),
      ));
    }
  }

  void exportToPdf({ResumeTemplateModel? template}) async {
    if (state.currentResume == null) return;

    emit(state.copyWith(isExporting: true, errorMessage: null));

    try {
      // Use the new export method with limit checking
      await _resumeUseCases.exportToPdfWithLimitCheck(state.currentResume!, template: template);
      emit(state.copyWith(
        isExporting: false,
        errorMessage: null,
      ));

      // Track export activity
      _trackActivity(
        type: ActivityType.resumeExported,
        resumeId: state.currentResume!.id,
        resumeName: _getResumeName(state.currentResume!),
      );
    } catch (e) {
      if (e is ExportLimitExceededException) {
        emit(state.copyWith(
          isExporting: false,
          errorMessage: 'Export limit reached. Please upgrade to premium for unlimited exports.',
        ));
      } else {
        emit(state.copyWith(
          isExporting: false,
          errorMessage: e.toString(),
        ));
      }
    }
  }

  void addLanguage(LanguageModel language) {
    if (state.currentResume != null) {
      final updatedLanguages = List<LanguageModel>.from(
        state.currentResume!.languages,
      )..add(language);

      final updatedResume = state.currentResume!.copyWith(
        languages: updatedLanguages,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
      _scheduleAutoSave();
    }
  }

  void updateLanguage(int index, LanguageModel language) {
    if (state.currentResume != null && index < state.currentResume!.languages.length) {
      final updatedLanguages = List<LanguageModel>.from(
        state.currentResume!.languages,
      );
      updatedLanguages[index] = language;

      final updatedResume = state.currentResume!.copyWith(
        languages: updatedLanguages,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
    }
  }

  void removeLanguage(int index) {
    if (state.currentResume != null && index < state.currentResume!.languages.length) {
      final updatedLanguages = List<LanguageModel>.from(
        state.currentResume!.languages,
      )..removeAt(index);

      final updatedResume = state.currentResume!.copyWith(
        languages: updatedLanguages,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
    }
  }

  void addCertification(CertificationModel certification) {
    if (state.currentResume != null) {
      final updatedCertifications = List<CertificationModel>.from(
        state.currentResume!.certifications,
      )..add(certification);

      final updatedResume = state.currentResume!.copyWith(
        certifications: updatedCertifications,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
      _scheduleAutoSave();
    }
  }

  void updateCertification(int index, CertificationModel certification) {
    if (state.currentResume != null && index < state.currentResume!.certifications.length) {
      final updatedCertifications = List<CertificationModel>.from(
        state.currentResume!.certifications,
      );
      updatedCertifications[index] = certification;

      final updatedResume = state.currentResume!.copyWith(
        certifications: updatedCertifications,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
    }
  }

  void removeCertification(int index) {
    if (state.currentResume != null && index < state.currentResume!.certifications.length) {
      final updatedCertifications = List<CertificationModel>.from(
        state.currentResume!.certifications,
      )..removeAt(index);

      final updatedResume = state.currentResume!.copyWith(
        certifications: updatedCertifications,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(
        currentResume: updatedResume,
        hasUnsavedChanges: true,
      ));
    }
  }

  void clearError() {
    emit(state.copyWith(errorMessage: null));
  }

  // Helper methods for activity tracking
  void _trackActivity({
    required ActivityType type,
    required String resumeId,
    required String resumeName,
    String? sectionName,
    Map<String, dynamic>? metadata,
  }) {
    try {
      _activityUseCases.trackActivity(
        type: type,
        resumeId: resumeId,
        resumeName: resumeName,
        sectionName: sectionName,
        metadata: metadata,
      );
    } catch (e) {
      // Silently fail activity tracking to not disrupt main functionality
      print('Failed to track activity: $e');
    }
  }

  String _getResumeName(ResumeModel resume) {
    final firstName = resume.personalInfo.firstName.trim();
    final lastName = resume.personalInfo.lastName.trim();

    if (firstName.isNotEmpty || lastName.isNotEmpty) {
      return '$firstName $lastName'.trim();
    }

    return 'Untitled Resume';
  }
}

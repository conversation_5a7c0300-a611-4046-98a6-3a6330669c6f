import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../domain/usecases/user_export_usecases.dart';
import '../../presentation/cubits/remote_config/remote_config_cubit.dart';
import '../../injection/injection_container.dart';
import '../pages/premium_upgrade_page.dart';

/// Widget that displays user's export status and limits
class ExportStatusWidget extends StatefulWidget {
  final bool showUpgradeButton;
  final VoidCallback? onUpgradePressed;

  const ExportStatusWidget({
    super.key,
    this.showUpgradeButton = true,
    this.onUpgradePressed,
  });

  @override
  State<ExportStatusWidget> createState() => _ExportStatusWidgetState();
}

class _ExportStatusWidgetState extends State<ExportStatusWidget> {
  ExportStatusInfo? _exportStatus;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadExportStatus();
  }

  Future<void> _loadExportStatus() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        setState(() {
          _error = 'User not authenticated';
          _isLoading = false;
        });
        return;
      }

      // Get remote config
      final remoteConfigCubit = context.read<RemoteConfigCubit>();
      final config = remoteConfigCubit.state is RemoteConfigLoaded
          ? (remoteConfigCubit.state as RemoteConfigLoaded).config
          : null;

      if (config == null) {
        setState(() {
          _error = 'Configuration not loaded';
          _isLoading = false;
        });
        return;
      }

      // Get export status
      final userExportUseCases = sl<UserExportUseCases>();
      final status = await userExportUseCases.getExportStatusInfo(
        user.uid,
        config.maxFreeExports,
        config.maxPremiumExports,
      );

      setState(() {
        _exportStatus = status;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              SizedBox(width: 12),
              Text('Loading export status...'),
            ],
          ),
        ),
      );
    }

    if (_error != null) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              const Icon(Icons.error, color: Colors.orange),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Unable to load export status: $_error',
                  style: const TextStyle(color: Colors.orange),
                ),
              ),
            ],
          ),
        ),
      );
    }

    final status = _exportStatus!;
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  status.canExport ? Icons.check_circle : Icons.warning,
                  color: status.canExport ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Export Status',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (status.isPremiumUser)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.amber,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'PREMIUM',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              status.statusMessage,
              style: theme.textTheme.bodyMedium,
            ),
            if (status.remainingExports != -1) ...[
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: status.maxAllowed > 0
                    ? status.currentCount / status.maxAllowed
                    : 0,
                backgroundColor: Colors.grey.shade300,
                valueColor: AlwaysStoppedAnimation<Color>(
                  status.canExport ? Colors.green : Colors.red,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '${status.currentCount} of ${status.maxAllowed} exports used',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.grey.shade600,
                ),
              ),
            ],
            if (!status.canExport && !status.isPremiumUser && widget.showUpgradeButton) ...[
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: widget.onUpgradePressed ?? () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const PremiumUpgradePage(),
                      ),
                    );
                  },
                  icon: const Icon(Icons.upgrade),
                  label: const Text('Upgrade to Premium'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.amber,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
            if (status.lastExportDate != null) ...[
              const SizedBox(height: 8),
              Text(
                'Last export: ${_formatDate(status.lastExportDate!)}',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}

/// Simple export status indicator for app bars or toolbars
class ExportStatusIndicator extends StatelessWidget {
  const ExportStatusIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<ExportStatusInfo?>(
      future: _getExportStatus(context),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          );
        }

        final status = snapshot.data;
        if (status == null) return const SizedBox.shrink();

        return Tooltip(
          message: status.statusMessage,
          child: Icon(
            status.canExport ? Icons.check_circle : Icons.warning,
            color: status.canExport ? Colors.green : Colors.orange,
            size: 20,
          ),
        );
      },
    );
  }

  Future<ExportStatusInfo?> _getExportStatus(BuildContext context) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return null;

      final remoteConfigCubit = context.read<RemoteConfigCubit>();
      final config = remoteConfigCubit.state is RemoteConfigLoaded
          ? (remoteConfigCubit.state as RemoteConfigLoaded).config
          : null;

      if (config == null) return null;

      final userExportUseCases = sl<UserExportUseCases>();
      return await userExportUseCases.getExportStatusInfo(
        user.uid,
        config.maxFreeExports,
        config.maxPremiumExports,
      );
    } catch (e) {
      return null;
    }
  }
}
